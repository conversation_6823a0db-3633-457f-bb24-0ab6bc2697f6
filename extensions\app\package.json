{"package_version": 2, "name": "app", "version": "2.0.0", "author": "向前", "editor": ">=3.8.0", "license": "MIT", "description": "i18n:app.description", "main": "./engine/dist/main.js", "contributions": {"builder": "./engine/dist/builder/index.js", "scene": {"script": "./engine/dist/scene.js"}, "asset-db": {"mount": {"path": "./assets", "readonly": true}}, "inspector": {"section": {"asset": {"directory": "./engine/dist/inspector/asset-directory.js"}}}, "messages": {"open-panel": {"methods": ["open-panel"]}, "open-wiki": {"methods": ["open-wiki"]}, "open-issues": {"methods": ["open-issues"]}, "open-github": {"methods": ["open-github"]}, "open-store": {"methods": ["open-store"]}, "refresh-executor": {"methods": ["refresh-executor"]}, "scene:ready": {"methods": ["scene:ready"]}, "asset-db:ready": {"methods": ["asset-db:ready"]}, "asset-db:asset-add": {"methods": ["asset-db:asset-add"]}, "asset-db:asset-change": {"methods": ["asset-db:asset-change"]}, "asset-db:asset-delete": {"methods": ["asset-db:asset-delete"]}}, "menu": [{"path": "i18n:app.app", "label": "i18n:app.create", "message": "open-panel"}, {"path": "i18n:app.app", "label": "i18n:app.refresh", "message": "refresh-executor"}, {"path": "i18n:app.app/i18n:app.help", "label": "文档", "message": "open-wiki"}, {"path": "i18n:app.app/i18n:app.help", "label": "反馈", "message": "open-issues"}, {"path": "i18n:app.app/i18n:app.help", "label": "商店", "message": "open-store"}, {"path": "i18n:app.app/i18n:app.help", "label": "GitHub🌟", "message": "open-github"}], "assets": {"menu": {"methods": "./engine/dist/menu/index.js", "createMenu": "onCreateMenu", "assetMenu": "onAssetMenu", "dbMenu": "onDBMenu", "panelMenu": "onPanelMenu"}}}, "panels": {"open-panel": {"title": "创建", "type": "dockable", "main": "./engine/dist/panel", "size": {"min-width": 400, "min-height": 300, "width": 600, "height": 600}}}, "scripts": {"build": "npm run --prefix ./engine build", "watch": "npm run --prefix ./engine watch"}}